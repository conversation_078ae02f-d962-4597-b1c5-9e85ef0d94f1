{"$schema": "https://zed.dev/schema/themes/v0.1.0.json", "name": "macOS Classic", "author": "hua<PERSON><PERSON><PERSON>", "themes": [{"name": "macOS Classic Light", "appearance": "light", "style": {"border": "#D2D2D2", "border.variant": "#E0E0E0", "border.focused": "#eeeeee", "border.selected": "#DADADA", "border.transparent": "#DADADA", "border.disabled": "#DCDBDA", "elevated_surface.background": "#F7F7F7", "surface.background": "#F9F9F9", "background": "#ffffff", "element.background": "#E0E0E0", "element.hover": "#D0D0D0", "element.active": null, "element.selected": "#C7DEFF", "element.disabled": null, "drop_target.background": null, "ghost_element.background": null, "ghost_element.hover": "#D7D5D577", "ghost_element.active": null, "ghost_element.selected": "#E4E0E0", "ghost_element.disabled": null, "text": "#000000", "text.muted": "#505050", "text.placeholder": "#929292", "text.disabled": null, "text.accent": "#1f6ae2", "icon": null, "icon.muted": null, "icon.disabled": null, "icon.placeholder": null, "icon.accent": null, "status_bar.background": "#E9E9E9", "title_bar.background": "#FEFEFE", "toolbar.background": "#FFFFFF", "tab_bar.background": "#E9E9E9", "tab.inactive_background": "#E9E9E9", "tab.active_background": "#FFFFFF", "search.match_background": "#CBCFDECC", "panel.background": "#F9F9F9", "panel.focused_border": null, "pane.focused_border": null, "scrollbar.thumb.background": "#C8C8C8AA", "scrollbar.thumb.hover_background": "#C8C8C8AA", "scrollbar.thumb.border": null, "scrollbar.track.background": "#ffffff", "scrollbar.track.border": null, "editor.foreground": "#000000", "editor.background": "#ffffff", "editor.gutter.background": "#FFFFFF", "editor.subheader.background": null, "editor.active_line.background": "#F0F0F0", "editor.highlighted_line.background": null, "editor.line_number": "#929292", "editor.active_line_number": "#000000", "editor.invisible": "#acafb1ff", "editor.wrap_guide": "#DCDBDA", "editor.active_wrap_guide": "#DCDBDA", "editor.document_highlight.read_background": "#C9CDD0BB", "editor.document_highlight.write_background": "#acafb166", "terminal.background": "#ffffff", "terminal.foreground": "#000000", "terminal.bright_foreground": null, "terminal.dim_foreground": null, "terminal.ansi.black": "#000000", "terminal.ansi.bright_black": "#4A4A4A", "terminal.ansi.dim_black": null, "terminal.ansi.red": "#C5060B", "terminal.ansi.bright_red": "#D9564E", "terminal.ansi.dim_red": null, "terminal.ansi.green": "#277F2B", "terminal.ansi.bright_green": "#35BD33", "terminal.ansi.dim_green": null, "terminal.ansi.yellow": "#8E7823", "terminal.ansi.bright_yellow": "#BDA400", "terminal.ansi.dim_yellow": null, "terminal.ansi.blue": "#282BFF", "terminal.ansi.bright_blue": "#5685F4", "terminal.ansi.dim_blue": null, "terminal.ansi.magenta": "#AE30C2", "terminal.ansi.bright_magenta": "#D75CE7", "terminal.ansi.dim_magenta": null, "terminal.ansi.cyan": "#00767C", "terminal.ansi.bright_cyan": "#20B1C9", "terminal.ansi.dim_cyan": null, "terminal.ansi.white": "#ffffff", "terminal.ansi.bright_white": "#999999", "terminal.ansi.dim_white": null, "link_text.hover": null, "conflict": "#C5060B", "conflict.background": null, "conflict.border": null, "created": "#1642FF", "created.background": "#e5ffe9", "created.border": null, "deleted": null, "deleted.background": "#FBEAE5", "deleted.border": null, "error": null, "error.background": "#FBEAE5", "error.border": "#EC9F89", "hidden": "#6D6D6D", "hidden.background": null, "hidden.border": null, "hint": null, "hint.background": "#E5F2FF", "hint.border": "#99CCFF", "ignored": null, "ignored.background": null, "ignored.border": null, "info": null, "info.background": "#E5EAFF", "info.border": "#8DA1FF", "modified": "#9e7008", "modified.background": "#fff2e5", "modified.border": null, "predictive": "#A4ABB6", "predictive.background": null, "predictive.border": null, "renamed": null, "renamed.background": null, "renamed.border": null, "success": null, "success.background": "#E5FFE5", "success.border": null, "unreachable": null, "unreachable.background": null, "unreachable.border": null, "warning": "#C99401", "warning.background": "#FFFBE5", "warning.border": "#D9CC89", "players": [{"cursor": "#3b9ee5ff", "background": "#3b9ee5ff", "selection": "#3b9ee53d"}, {"cursor": "#55b4d3ff", "background": "#55b4d3ff", "selection": "#55b4d33d"}, {"cursor": "#f98d3fff", "background": "#f98d3fff", "selection": "#f98d3f3d"}, {"cursor": "#a37accff", "background": "#a37accff", "selection": "#a37acc3d"}, {"cursor": "#4dbf99ff", "background": "#4dbf99ff", "selection": "#4dbf993d"}, {"cursor": "#ef7271ff", "background": "#ef7271ff", "selection": "#ef72713d"}, {"cursor": "#f1ad49ff", "background": "#f1ad49ff", "selection": "#f1ad493d"}, {"cursor": "#85b304ff", "background": "#85b304ff", "selection": "#85b3043d"}], "syntax": {"attribute": {"color": "#957931", "font_style": null, "font_weight": null}, "boolean": {"color": "#C5060B", "font_style": null, "font_weight": null}, "comment": {"color": "#007fff", "font_style": null, "font_weight": null}, "comment.doc": {"color": "#007fff", "font_style": null, "font_weight": null}, "constant": {"color": "#C5060B", "font_style": null, "font_weight": null}, "constructor": {"color": "#0433ff", "font_style": null, "font_weight": null}, "embedded": {"color": "#333333", "font_style": null, "font_weight": null}, "function": {"color": "#0000A2", "font_style": null, "font_weight": null}, "keyword": {"color": "#0433ff", "font_style": null, "font_weight": null}, "link_text": {"color": "#0000A2", "font_style": "normal", "font_weight": null}, "link_uri": {"color": "#6A7293", "font_style": "italic", "font_weight": null}, "number": {"color": "#0433ff", "font_style": null, "font_weight": null}, "string": {"color": "#036A07", "font_style": null, "font_weight": null}, "string.escape": {"color": "#036A07", "font_style": null, "font_weight": null}, "string.regex": {"color": "#036A07", "font_style": null, "font_weight": null}, "string.special": {"color": "#d21f07", "font_style": null, "font_weight": null}, "string.special.symbol": {"color": "#d21f07", "font_style": null, "font_weight": null}, "tag": {"color": "#0433ff", "font_style": null, "font_weight": null}, "text.literal": {"color": "#6F42C1", "font_style": null, "font_weight": null}, "title": {"color": "#0433FF", "font_style": null, "font_weight": null}, "type": {"color": "#6f42c1", "font_style": null, "font_weight": null}, "property": {"color": "#333333", "font_style": null, "font_weight": null}, "variable": {"color": "#333333", "font_style": null, "font_weight": null}, "variable.special": {"color": "#C5060B", "font_style": null, "font_weight": null}}}}, {"name": "macOS Classic Dark", "appearance": "dark", "style": {"border": "#404040", "border.variant": "#3A3A3A", "border.focused": "#3A3A3AFF", "border.selected": "#3A3A3A", "border.transparent": "#3A3A3A", "border.disabled": "#3A3A3A", "elevated_surface.background": "#1E1D1E", "surface.background": "#1E1D1E", "background": "#131313", "element.background": "#474646", "element.hover": "#353436", "element.active": "#353436", "element.selected": "#353436", "element.disabled": null, "drop_target.background": null, "ghost_element.background": null, "ghost_element.hover": "#353436", "ghost_element.active": null, "ghost_element.selected": "#474646", "ghost_element.disabled": null, "text": "#CACCCA", "text.muted": "#9E9E9E", "text.placeholder": null, "text.disabled": null, "text.accent": null, "icon": null, "icon.muted": null, "icon.disabled": null, "icon.placeholder": null, "icon.accent": null, "status_bar.background": "#363636", "title_bar.background": "#323232", "toolbar.background": "#131313", "tab_bar.background": "#232323", "tab.inactive_background": "#232323", "tab.active_background": "#131313", "search.match_background": null, "panel.background": "#1E1D1E", "panel.focused_border": null, "pane.focused_border": null, "scrollbar.thumb.background": "#4C4D4DAA", "scrollbar.thumb.hover_background": "#4C4D4D", "scrollbar.thumb.border": null, "scrollbar.track.background": "#131313", "scrollbar.track.border": null, "editor.foreground": "#DDDDDD", "editor.background": "#131313", "editor.gutter.background": "#131313", "editor.subheader.background": null, "editor.active_line.background": "#272727", "editor.highlighted_line.background": null, "editor.line_number": "#8F8F8F", "editor.active_line_number": "#DDDDDD", "editor.invisible": null, "editor.wrap_guide": "#3A3A3A", "editor.active_wrap_guide": "#3A3A3A", "editor.document_highlight.read_background": null, "editor.document_highlight.write_background": null, "terminal.background": null, "terminal.foreground": null, "terminal.bright_foreground": null, "terminal.dim_foreground": null, "terminal.ansi.black": "#E8E4CF", "terminal.ansi.bright_black": "#57564F", "terminal.ansi.dim_black": null, "terminal.ansi.red": "#A8473B", "terminal.ansi.bright_red": "#DD6F61", "terminal.ansi.dim_red": null, "terminal.ansi.green": "#76BA53", "terminal.ansi.bright_green": "#9DE478", "terminal.ansi.dim_green": null, "terminal.ansi.yellow": "#E1D797", "terminal.ansi.bright_yellow": "#857F5C", "terminal.ansi.dim_yellow": null, "terminal.ansi.blue": "#3f72e2", "terminal.ansi.bright_blue": "#81A9F6", "terminal.ansi.dim_blue": null, "terminal.ansi.magenta": "#AE30C2", "terminal.ansi.bright_magenta": "#D86DE9", "terminal.ansi.dim_magenta": null, "terminal.ansi.cyan": "#3DB6B0", "terminal.ansi.bright_cyan": "#5BDFD8", "terminal.ansi.dim_cyan": null, "terminal.ansi.white": "#131313", "terminal.ansi.bright_white": "#3A3A3A", "terminal.ansi.dim_white": null, "link_text.hover": null, "conflict": "#D2602D", "conflict.background": null, "conflict.border": null, "created": "#3f72e2", "created.background": "#0C4619", "created.border": null, "deleted": null, "deleted.background": "#46190C", "deleted.border": null, "error": null, "error.background": "#46190C", "error.border": "#802207", "hidden": "#9E9E9E", "hidden.background": null, "hidden.border": null, "hint": null, "hint.background": "#0C194D", "hint.border": "#082190", "ignored": null, "ignored.background": null, "ignored.border": null, "info": null, "info.background": "#0C194D", "info.border": "#082190", "modified": "#B0A878", "modified.background": "#3A310E", "modified.border": null, "predictive": "#5D5945", "predictive.background": null, "predictive.border": null, "renamed": null, "renamed.background": null, "renamed.border": null, "success": null, "success.background": "#0C4619", "success.border": null, "unreachable": null, "unreachable.background": null, "unreachable.border": null, "warning": null, "warning.background": "#3A310E", "warning.border": "#7B6508", "players": [{"cursor": "#72<PERSON><PERSON><PERSON>", "background": "#72<PERSON><PERSON><PERSON>", "selection": "#72cffe3d"}, {"cursor": "#5bcde5ff", "background": "#5bcde5ff", "selection": "#5bcde53d"}, {"cursor": "#fead66ff", "background": "#fead66ff", "selection": "#fead663d"}, {"cursor": "#debffeff", "background": "#debffeff", "selection": "#debffe3d"}, {"cursor": "#95e5cbff", "background": "#95e5cbff", "selection": "#95e5cb3d"}, {"cursor": "#f18779ff", "background": "#f18779ff", "selection": "#f187793d"}, {"cursor": "#fecf72ff", "background": "#fecf72ff", "selection": "#fecf723d"}, {"cursor": "#d5fe80ff", "background": "#d5fe80ff", "selection": "#d5fe803d"}], "syntax": {"attribute": {"color": "#be9a52", "font_style": null, "font_weight": null}, "boolean": {"color": "#E1D797", "font_style": null, "font_weight": null}, "comment": {"color": "#9E9E9E", "font_style": null, "font_weight": null}, "comment.doc": {"color": "#9E9E9E", "font_style": null, "font_weight": null}, "constant": {"color": "#E1D797", "font_style": null, "font_weight": null}, "constructor": {"color": "#b5af9a", "font_style": null, "font_weight": null}, "embedded": {"color": "#CACCCA", "font_style": null, "font_weight": null}, "function": {"color": "#E1D797", "font_style": null, "font_weight": null}, "keyword": {"color": "#E19773", "font_style": null, "font_weight": null}, "link_text": {"color": "#A86D3B", "font_style": "normal", "font_weight": null}, "link_uri": {"color": "#6F6D66", "font_style": "italic", "font_weight": null}, "number": {"color": "#E19773", "font_style": null, "font_weight": null}, "string": {"color": "#76BA53", "font_style": null, "font_weight": null}, "string.escape": {"color": "#76BA53", "font_style": null, "font_weight": null}, "string.regex": {"color": "#76BA53", "font_style": null, "font_weight": null}, "string.special": {"color": "#E1D797", "font_style": null, "font_weight": null}, "string.special.symbol": {"color": "#E1D797", "font_style": null, "font_weight": null}, "tag": {"color": "#b5af9a", "font_style": null, "font_weight": null}, "text.literal": {"color": "#E1D797", "font_style": null, "font_weight": null}, "title": {"color": "#A76D3B", "font_style": null, "font_weight": 600}, "type": {"color": "#A86D3B", "font_style": null, "font_weight": null}, "variable.special": {"color": "#E19773", "font_style": null, "font_weight": null}}}}, {"name": "macOS Classic Dark2", "appearance": "dark", "style": {"border": "#3A3A3A", "border.variant": "#3A3A3A", "border.focused": "#3A3A3AFF", "border.selected": "#3A3A3A", "border.transparent": "#3A3A3A", "border.disabled": "#3A3A3A", "elevated_surface.background": "#1E1D1E", "surface.background": "#1E1D1E", "background": "#131313", "element.background": "#282828", "element.hover": "#252426", "element.active": "#252426", "element.selected": "#353436", "element.disabled": null, "drop_target.background": null, "ghost_element.background": null, "ghost_element.hover": "#E8E4CF11", "ghost_element.active": null, "ghost_element.selected": "#E8E4CF22", "ghost_element.disabled": null, "text": "#CACCCA", "text.muted": "#9E9E9E", "text.placeholder": null, "text.disabled": null, "text.accent": null, "icon": null, "icon.muted": null, "icon.disabled": null, "icon.placeholder": null, "icon.accent": null, "status_bar.background": "#262626", "title_bar.background": "#292929", "toolbar.background": "#131313", "tab_bar.background": "#232323", "tab.inactive_background": "#232323", "tab.active_background": "#101010", "search.match_background": null, "panel.background": "#1E1D1E", "panel.focused_border": null, "pane.focused_border": null, "scrollbar.thumb.background": "#4C4D4DAA", "scrollbar.thumb.hover_background": "#4C4D4D", "scrollbar.thumb.border": null, "scrollbar.track.background": "#131313", "scrollbar.track.border": null, "editor.foreground": "#E8E4CF", "editor.background": "#131313", "editor.gutter.background": "#131313", "editor.subheader.background": null, "editor.active_line.background": "#272727", "editor.highlighted_line.background": null, "editor.line_number": "#8F8F8F", "editor.active_line_number": "#E8E4CF", "editor.invisible": null, "editor.wrap_guide": "#3A3A3A", "editor.active_wrap_guide": "#3A3A3A", "editor.document_highlight.read_background": null, "editor.document_highlight.write_background": null, "terminal.background": null, "terminal.foreground": null, "terminal.bright_foreground": null, "terminal.dim_foreground": null, "terminal.ansi.black": "#E8E4CF", "terminal.ansi.bright_black": "#57564F", "terminal.ansi.dim_black": null, "terminal.ansi.red": "#D73737", "terminal.ansi.bright_red": "#E66969", "terminal.ansi.dim_red": null, "terminal.ansi.green": "#76BA53", "terminal.ansi.bright_green": "#9DE478", "terminal.ansi.dim_green": null, "terminal.ansi.yellow": "#AE9513", "terminal.ansi.bright_yellow": "#705F00", "terminal.ansi.dim_yellow": null, "terminal.ansi.blue": "#3f72e2", "terminal.ansi.bright_blue": "#81A9F6", "terminal.ansi.dim_blue": null, "terminal.ansi.magenta": "#AE30C2", "terminal.ansi.bright_magenta": "#D86DE9", "terminal.ansi.dim_magenta": null, "terminal.ansi.cyan": "#37D0D7", "terminal.ansi.bright_cyan": "#69DDE6", "terminal.ansi.dim_cyan": null, "terminal.ansi.white": "#131313", "terminal.ansi.bright_white": "#3A3A3A", "terminal.ansi.dim_white": null, "link_text.hover": null, "conflict": "#AE9513", "conflict.background": null, "conflict.border": null, "created": "#3f72e2", "created.background": "#0C4619", "created.border": null, "deleted": null, "deleted.background": "#46190C", "deleted.border": null, "error": null, "error.background": "#46190C", "error.border": "#802207", "hidden": "#9E9E9E", "hidden.background": null, "hidden.border": null, "hint": null, "hint.background": "#0C194D", "hint.border": "#082190", "ignored": null, "ignored.background": null, "ignored.border": null, "info": null, "info.background": "#0C194D", "info.border": "#082190", "modified": "#AE9513", "modified.background": "#3A310E", "modified.border": null, "predictive": "#5D5945", "predictive.background": null, "predictive.border": null, "renamed": null, "renamed.background": null, "renamed.border": null, "success": null, "success.background": "#0C4619", "success.border": null, "unreachable": null, "unreachable.background": null, "unreachable.border": null, "warning": null, "warning.background": "#3A310E", "warning.border": "#7B6508", "players": [{"cursor": "#72<PERSON><PERSON><PERSON>", "background": "#72<PERSON><PERSON><PERSON>", "selection": "#72cffe3d"}, {"cursor": "#5bcde5ff", "background": "#5bcde5ff", "selection": "#5bcde53d"}, {"cursor": "#fead66ff", "background": "#fead66ff", "selection": "#fead663d"}, {"cursor": "#debffeff", "background": "#debffeff", "selection": "#debffe3d"}, {"cursor": "#95e5cbff", "background": "#95e5cbff", "selection": "#95e5cb3d"}, {"cursor": "#f18779ff", "background": "#f18779ff", "selection": "#f187793d"}, {"cursor": "#fecf72ff", "background": "#fecf72ff", "selection": "#fecf723d"}, {"cursor": "#d5fe80ff", "background": "#d5fe80ff", "selection": "#d5fe803d"}], "syntax": {"attribute": {"color": "#AE9513", "font_style": null, "font_weight": null}, "boolean": {"color": "#705f00", "font_style": null, "font_weight": null}, "comment": {"color": "#9E9E9E", "font_style": null, "font_weight": null}, "comment.doc": {"color": "#9E9E9E", "font_style": null, "font_weight": null}, "constant": {"color": "#705f00", "font_style": null, "font_weight": null}, "constructor": {"color": "#9a9576", "font_style": null, "font_weight": null}, "embedded": {"color": "#CACCCA", "font_style": null, "font_weight": null}, "function": {"color": "#c0b298", "font_style": null, "font_weight": null}, "keyword": {"color": "#AE9513", "font_style": null, "font_weight": null}, "link_text": {"color": "#CEB63D", "font_style": "normal", "font_weight": null}, "link_uri": {"color": "#6E6C65", "font_style": "italic", "font_weight": null}, "number": {"color": "#AE9513", "font_style": null, "font_weight": null}, "string": {"color": "#76BA53", "font_style": null, "font_weight": null}, "string.escape": {"color": "#76BA53", "font_style": null, "font_weight": null}, "string.regex": {"color": "#76BA53", "font_style": null, "font_weight": null}, "string.special": {"color": "#7D7A68", "font_style": null, "font_weight": null}, "string.special.symbol": {"color": "#7D7A68", "font_style": null, "font_weight": null}, "tag": {"color": "#9a9576", "font_style": null, "font_weight": null}, "text.literal": {"color": "#8C7701", "font_style": null, "font_weight": null}, "title": {"color": "#D73737", "font_style": null, "font_weight": null}, "type": {"color": "#D73737", "font_style": null, "font_weight": null}, "variable.special": {"color": "#AE9513"}}}}]}